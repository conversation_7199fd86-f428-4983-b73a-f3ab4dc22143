/* 简化后的模型卡片组件样式 - 仅显示模型名称和图片 */
.model-card {
  display: flex;
  width: 100%;
  aspect-ratio: 1/1; /* 1:1 比例 */
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  border-radius: 16px;
  padding: 16px;
  border: 1px solid var(--card-border-default);
  background: linear-gradient(144deg, rgba(0, 0, 0, 0.30) 0%, rgba(255, 255, 255, 0.05) 98.73%);
  box-shadow: var(--card-shadow-inset);
  backdrop-filter: blur(15px);
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
}

.model-card:hover {
  border-color: var(--card-border-hover);
  box-shadow: var(--card-shadow-hover);
  transform: translateY(-4px);
}

/* 图片容器 */
.model-card__image-container {
  position: relative;
  width: 100%;
  height: calc(100% - 50px); /* 减去标题高度 */
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--card-bg-overlay);
}

.model-card__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.model-card:hover .model-card__image {
  transform: scale(1.05);
}

.model-card__title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0 16px;
  width: 100%;
  text-align: center;
  order: 1;
}

.model-card__image-container {
  order: 2;
  position: relative;
  width: 100%;
  height: calc(100% - 50px);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--card-bg-overlay);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .model-card {
    width: 200px;
    height: 240px;
  }


}